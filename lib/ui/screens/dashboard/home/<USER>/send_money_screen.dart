// ignore_for_file: use_build_context_synchronously

import 'package:collection/collection.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendMoneyScreen extends StatefulWidget {
  const SendMoneyScreen({super.key, this.arg});

  final SendMoneyArg? arg;

  @override
  State<SendMoneyScreen> createState() => _SendMoneyScreenState();
}

class _SendMoneyScreenState extends State<SendMoneyScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _init();
      setTransferReason();
    });
  }

  setTransferReason() {
    final beneficiary = widget.arg?.beneficiary;
    if (beneficiary != null) {
      if (widget.arg?.purpose != null) {
        context.read<TransactionVM>().setSelectedReason(widget.arg?.purpose);
      } else {
        context
            .read<TransactionVM>()
            .setSelectedReason(beneficiary.latestTransaction?.purpose);
      }
    }
  }

  Future<void> _init() async {
    final walletVM = context.read<WalletVM>();
    final sendMoneyVM = context.read<SendMoneyVM>();

    await sendMoneyVM.init(
      fromWalletByDefault:
          _setWalletFromCurrency(widget.arg?.fromCurrencyId ?? 0),
      recipientCurrency: _setCurrency(widget.arg?.toCurrencyId ?? 0),
      walletList: walletVM.walletList,
    );

    await sendMoneyVM.getConversionRate();
    await sendMoneyVM.getFees();
    fetchNewCustomerRateEligibility(
      sendMoneyVM.fromConvertWallet?.currency?.id ?? 0,
      sendMoneyVM.recipientCurrency?.id ?? 0,
    );

    // To check if user input exceeds currency limits
    context.read<CurrencyVM>().getCurrencyRatesLimits(
        sendMoneyVM.fromConvertWallet?.currency?.id ?? 0);

    if (widget.arg != null) {
      _prePopulateFromSendMoneyArg(widget.arg!);
    }
  }

  // Checks for new customer rate eligibility
  Future<void> fetchNewCustomerRateEligibility(int fromId, int toId) async {
    final sendMoneyVm = context.read<SendMoneyVM>();
    final rateVM = context.read<RateVm>();

    final res = await rateVM.getNewCustomerRate(fromId, toId);

    handleApiResponse(
      response: res,
      showSuccessToast: false,
      onSuccess: () {
        sendMoneyVm.checkElligibiltyForNewCustomerRate(
          rateElligibility: rateVM.showNewRateModal,
          rateAmont: rateVM
              .newCustomerRateModel?.rateData?.newCustomerRateTransactionAmount,
        );
        _showNewCustomerOfferModal();
      },
    );
  }

  void _prePopulateFromSendMoneyArg(SendMoneyArg arg) {
    final sendMoneyVM = context.read<SendMoneyVM>();
    if (arg.fromAmount != null) {
      sendMoneyVM.fromC.text = arg.fromAmount!;
      sendMoneyVM
        ..setFromDecimalValue(arg.fromAmount!.replaceAllCommas())
        ..setRecipientGetAmountToTextField();
    }
    setState(() {});
  }

  /// Returns the wallet whose currency matches the provided [currencyId].
  /// If no match is found, returns `null`.
  Wallet _setWalletFromCurrency(int currencyId) {
    final walletVM = context.read<WalletVM>();
    final wallet = walletVM.walletList
        .firstWhereOrNull((wallet) => wallet.currency?.id == currencyId);
    if (wallet == null) {
      showWarningToast(
          "Wallet with currency ${widget.arg?.fromCode} not found");
      return walletVM.walletList.first;
    }
    return wallet;
  }

  Currency _setCurrency(int currencyId) {
    final currencyVM = context.read<CurrencyVM>();
    final recipientCurrencies =
        widget.arg?.fromWallet?.currency?.recipientCurrencies;

    // Fallback to first recipient currency when currencyId is 0 or missing
    if ((currencyId == 0) &&
        recipientCurrencies != null &&
        recipientCurrencies.isNotEmpty) {
      return recipientCurrencies.first;
    }

    // Validate that the requested currency is allowed for this wallet
    if (recipientCurrencies != null && currencyId != 0) {
      final allowed = recipientCurrencies.any((c) => c.id == currencyId);
      if (allowed) {
        return recipientCurrencies.firstWhere((c) => c.id == currencyId);
      }
      // If not allowed, fallback to the first allowed currency
      return recipientCurrencies.first;
    }

    // Last-resort lookup in the global currency list
    return currencyVM.currencies.firstWhere((c) => c.id == currencyId,
        orElse: () {
      printty('Currency $currencyId not found in global list',
          level: 'walletToCurrency');
      throw Exception('Currency $currencyId not available');
    });
  }

  _showNewCustomerOfferModal() {
    final rateVm = context.read<RateVm>();
    if (rateVm.showNewRateModal) {
      return BsWrapper.bottomSheet(
        context: context,
        widget: const NewCustomerOfferModal(),
      );
    }
  }

  /// Returns true if the amount the user wants to send exceeds the daily exchange-rate limit.
  bool get isCurrencyLimitsExceeded {
    final currencyVM = context.read<CurrencyVM>();
    final sendVm = context.read<SendMoneyVM>();

    final fromAmount = double.tryParse(sendVm.fromDecimalValue ?? '0') ?? 0;
    final remainingLimit = currencyVM.currencyRatesLimits?.remainingAmount;

    // If we don’t have a limit, treat it as unlimited (return false).
    if (remainingLimit == null) return false;

    return fromAmount > remainingLimit.toDouble();
  }

  @override
  Widget build(BuildContext context) {
    final currencyVM = context.watch<CurrencyVM>();
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Send Money',
            onBackBtnTap: () {
              vm.resetData();
              context.read<BankVM>().resetData();
              context.read<TransactionVM>().setSelectedReason(null);
              Navigator.pop(context);
            },
          ),
          body: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              Text(
                "Enter the amount you would like to send",
                style: AppTypography.text16.copyWith(color: AppColors.gray93),
              ),
              const YBox(24),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'You send',
                    style: AppTypography.text12.copyWith(
                      color: AppColors.textGray,
                    ),
                  ),
                  const YBox(4),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(16),
                      vertical: Sizer.height(12),
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      border: Border.all(
                          color: vm.amtSendIsGreaterThanBalance ||
                                  isCurrencyLimitsExceeded
                              ? AppColors.red
                              : AppColors.grayAEC),
                      borderRadius: BorderRadius.circular(Sizer.radius(12)),
                    ),
                    child: Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CurrencyCards(
                              isNetworkSvg:
                                  vm.fromConvertWallet?.currency?.flag != null,
                              currencyCode:
                                  vm.fromConvertWallet?.currency?.code ?? "",
                              showCurrencyCode: true,
                              flagIconPath:
                                  vm.fromConvertWallet?.currency?.flag ??
                                      AppImages.ngnFlag,
                              onTap: () async {
                                final res = await BsWrapper.bottomSheet(
                                  context: context,
                                  widget: SelectWalletCurrencySheet(
                                    fromConvert: true,
                                    isSendMoney: true,
                                    selectedCurrencyCode:
                                        vm.fromConvertWallet?.currency?.code,
                                  ),
                                );

                                if (res == true) {
                                  fetchNewCustomerRateEligibility(
                                    vm.fromConvertWallet?.currency?.id ?? 0,
                                    vm.recipientCurrency?.id ?? 0,
                                  );
                                  currencyVM.getCurrencyRatesLimits(
                                      vm.fromConvertWallet?.currency?.id ?? 0);
                                }
                              },
                            ),
                            const YBox(8),
                            RichText(
                              text: TextSpan(
                                text: 'Balance: ',
                                style: AppTypography.text12.copyWith(
                                  color: AppColors.gray79,
                                  fontFamily: "Outfit",
                                  fontWeight: FontWeight.w300,
                                ),
                                children: [
                                  TextSpan(
                                    text: vm.fromConvertWalletBalance,
                                    style: AppTypography.text12.copyWith(
                                      color: AppColors.mainBlack,
                                      fontFamily: AppFont.inter.family,
                                      fontWeight: FontWeight.w300,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const Spacer(),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Container(
                              height: Sizer.height(30),
                              color: AppColors.white,
                              width: Sizer.width(120),
                              margin: EdgeInsets.only(
                                bottom: Sizer.width(6),
                              ),
                              child: ConvertTextfield(
                                hintText: "10,000.00",
                                textAlign: TextAlign.end,
                                color: vm.amtSendIsGreaterThanBalance ||
                                        isCurrencyLimitsExceeded
                                    ? AppColors.red
                                    : AppColors.baseBlack,
                                controller: vm.fromC,
                                onChanged: (val) async {
                                  printty(val, level: "val");

                                  // if (vm.fromConvertWallet?.currency?.code ==
                                  //     AppUtils.cadCurrency) {
                                  //   vm.setUserHasRateMinAmount(val.trim());
                                  // }

                                  vm
                                    ..setFromDecimalValue(
                                        val.trim().replaceAllCommas())
                                    ..setRecipientGetAmountToTextField()
                                    ..reBuildUI();
                                },
                              ),
                            ),
                            YBox(8),
                            vm.amtSendIsGreaterThanBalance
                                ? Text(
                                    "Balance exceeded",
                                    style: AppTypography.text12.copyWith(
                                      fontWeight: FontWeight.w300,
                                      color: AppColors.red,
                                    ),
                                  )
                                : isCurrencyLimitsExceeded
                                    ? Text(
                                        "${currencyVM.currencyRatesLimits?.limitType ?? ""} limit exceeded",
                                        style: AppTypography.text12.copyWith(
                                          fontWeight: FontWeight.w300,
                                          color: AppColors.red,
                                        ),
                                      )
                                    : InkWell(
                                        onTap: () {
                                          vm.fromC.text =
                                              (vm.fromConvertWallet?.balance ??
                                                      "0")
                                                  .toString();
                                          vm
                                            ..setFromDecimalValue(
                                                vm.fromConvertWallet?.balance ??
                                                    "0")
                                            ..setRecipientGetAmountToTextField()
                                            ..reBuildUI();
                                        },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: Sizer.width(8),
                                            vertical: Sizer.height(6),
                                          ),
                                          decoration: const BoxDecoration(
                                            color: AppColors.blueFD,
                                            borderRadius: BorderRadius.all(
                                              Radius.circular(8),
                                            ),
                                          ),
                                          child: Text(
                                            'Add all Funds',
                                            style:
                                                AppTypography.text12.copyWith(
                                              color: AppColors.blueE5,
                                            ),
                                          ),
                                        ),
                                      ),
                            const YBox(4),
                          ],
                        )
                      ],
                    ),
                  ),

                  SizedBox(
                    height: Sizer.height(140),
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Positioned(
                          top: 0,
                          left: 40,
                          child: Column(
                            children: [
                              SendMoneyHDivider(),
                              BuildRateCard(text: vm.rateFormat),
                              SendMoneyHDivider(),
                              BuildRateCard(
                                  text: "${vm.transferFee}  Transfer fee"),
                              SendMoneyHDivider(),
                              InkWell(
                                onTap: () async {
                                  await vm.swapCurrency();
                                  fetchNewCustomerRateEligibility(
                                    vm.fromConvertWallet?.currency?.id ?? 0,
                                    vm.recipientCurrency?.id ?? 0,
                                  );
                                  currencyVM.getCurrencyRatesLimits(
                                      vm.fromConvertWallet?.currency?.id ?? 0);
                                },
                                child: Container(
                                  padding: EdgeInsets.all(Sizer.radius(8)),
                                  decoration: BoxDecoration(
                                      color: AppColors.blue8FB,
                                      borderRadius: BorderRadius.circular(30),
                                      boxShadow: const [
                                        BoxShadow(
                                          color: AppColors.grayE9,
                                          blurRadius: 4,
                                          offset: Offset(0, 4),
                                        ),
                                      ]),
                                  // alignment: Alignment.center,
                                  child: Icon(
                                    Iconsax.arrow_swap,
                                    color: AppColors.primaryBlue,
                                    size: Sizer.radius(20),
                                  ),
                                ),
                              ),
                              SendMoneyHDivider(height: 50),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // if (vm.amtSendIsGreaterThanBalance)
                  //   Align(
                  //     alignment: Alignment.centerLeft,
                  //     child: Text(
                  //       'Amount you send is greater than your balance',
                  //       style: AppTypography.text11.copyWith(
                  //         color: AppColors.fail30,
                  //       ),
                  //     ),
                  //   ),
                  Column(
                    children: [
                      YBox(6),
                      Text(
                        'Recipient gets',
                        style: AppTypography.text12.copyWith(
                          color: AppColors.textGray,
                        ),
                      ),
                      YBox(4),
                    ],
                  ),

                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(16),
                      vertical: Sizer.height(26),
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.grayFE,
                      border: Border.all(color: AppColors.grayAEC),
                      borderRadius: BorderRadius.circular(Sizer.radius(12)),
                    ),
                    child: Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                            top: Sizer.height(6),
                          ),
                          child: CurrencyCards(
                            isNetworkSvg: vm.recipientCurrency?.flag != null,
                            currencyCode: vm.recipientCurrency?.code ?? "",
                            showCurrencyCode: true,
                            flagIconPath:
                                vm.recipientCurrency?.flag ?? AppImages.ngnFlag,
                            onTap: () async {
                              final res = await BsWrapper.bottomSheet(
                                context: context, //
                                widget: SelectCurrencySheet(
                                  selectedCurrencyCode:
                                      vm.recipientCurrency?.code,
                                ),
                              );
                              if (res == true) {
                                fetchNewCustomerRateEligibility(
                                  vm.fromConvertWallet?.currency?.id ?? 0,
                                  vm.recipientCurrency?.id ?? 0,
                                );
                              }
                            },
                          ),
                        ),
                        const Spacer(),
                        Container(
                          height: Sizer.height(30),
                          width: Sizer.width(150),
                          margin: EdgeInsets.only(
                            bottom: Sizer.height(10),
                          ),
                          child: ConvertTextfield(
                            hintText: "10,000.00",
                            textAlign: TextAlign.end,
                            color: vm.amtSendIsGreaterThanBalance ||
                                    vm.recipientMinimumAmountCheck
                                ? AppColors.fail30
                                : AppColors.baseBlack,
                            controller: vm.recipientC,
                            onChanged: (val) {
                              printty(val, level: "val");
                              // if (vm.recipientCurrency?.code ==
                              //     AppUtils.cadCurrency) {
                              //   vm.setUserHasRateMinAmount(val.trim());
                              // }
                              vm.senderGetAmount();
                              vm.reBuildUI();
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const YBox(24),
              Text(
                "Pay with",
                style: AppTypography.text12.copyWith(
                  color: AppColors.gray79,
                ),
              ),
              YBox(8),
              Row(
                children: [
                  SvgPicture.asset(
                    AppSvgs.walletSolid,
                    height: Sizer.height(20),
                  ),
                  XBox(8),
                  Text(
                    "${vm.fromConvertWallet?.currency?.code} Wallet",
                    style: AppTypography.text16.copyWith(
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  // Spacer(),
                  // SendTransButton(
                  //   buttonText: "Change",
                  //   bgColor: AppColors.grayF5,
                  //   textColor: AppColors.gray93,
                  //   onTap: () {},
                  // )
                ],
              ),
              const YBox(30),
              Row(
                children: [
                  Text(
                    "Reason for sending money",
                    style: AppTypography.text12.copyWith(
                      color: AppColors.gray79,
                    ),
                  ),
                  XBox(10),
                  Text(
                    "*",
                    style: AppTypography.text16.copyWith(
                      color: AppColors.red,
                    ),
                  ),
                ],
              ),
              YBox(6),
              Consumer<TransactionVM>(builder: (_, transactionVm, __) {
                return Container(
                  padding: transactionVm.showReasonWarning
                      ? EdgeInsets.symmetric(
                          horizontal: Sizer.width(12),
                          vertical: Sizer.height(12),
                        )
                      : null,
                  decoration: transactionVm.showReasonWarning
                      ? BoxDecoration(
                          border: Border.all(
                            color: AppColors.red.withValues(alpha: 0.5),
                          ),
                          borderRadius: BorderRadius.circular(Sizer.radius(12)),
                        )
                      : null,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          transactionVm.selectedReason?.name ??
                              'Choose a suitable reason',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: AppTypography.text16.copyWith(
                            color: transactionVm.selectedReason?.name == null
                                ? AppColors.gray93.withValues(alpha: 0.5)
                                : AppColors.mainBlack,
                          ),
                        ),
                      ),
                      const XBox(10),
                      SendTransButton(
                        buttonText: "Change",
                        onTap: () {
                          FocusScope.of(context).unfocus();
                          BsWrapper.bottomSheet(
                            context: context, //
                            widget: SendMoneyReasonSheet(
                              selectedReason:
                                  transactionVm.selectedReason?.name,
                            ),
                          );
                        },
                      )
                    ],
                  ),
                );
              }),
              YBox(40),
              CustomBtn.withChild(
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  child: ContinueText(
                    isOnline: vm.btnEnabled && !isCurrencyLimitsExceeded,
                  ),
                  onTap: () async {
                    FocusScope.of(context).unfocus();
                    if (context.read<TransactionVM>().selectedReason == null) {
                      context.read<TransactionVM>().setShowReasonWarning(true);
                      setState(() {});
                      showWarningToast(
                          "Please select a reason for sending money");
                      return;
                    }

                    if (widget.arg?.beneficiary != null) {
                      // Check for IBAN transfer method first
                      if (widget.arg?.beneficiary?.transferMethod ==
                          TransferMethod.iban) {
                        vm.setTransferMethodType(TransferMethodType.iban);
                        Navigator.pushNamed(
                          context,
                          RoutePath.sendToIbanScreen,
                          arguments: TransferMethodArg(
                            paymentMethod:
                                PaymentMethod(), // Not needed for IBAN
                            beneficiary: widget.arg?.beneficiary,
                          ),
                        );

                        return;
                      }
                      Navigator.pushNamed(context, RoutePath.reviewScreen,
                          arguments: SendMoneyReviewsArg(
                            transferAgainParams: _getSendMoneyParams(
                                widget.arg?.beneficiary?.transferMethod ?? ''),
                            name: widget.arg?.beneficiary?.accountName ?? '',
                            title: widget.arg?.beneficiary?.accountName ?? '',
                            iconPath: widget.arg?.beneficiary?.iconUrl ?? "",
                            subTitle:
                                "${widget.arg?.beneficiary?.institutionName} • ${widget.arg?.beneficiary?.accountIdentifier ?? ''}",
                          ));
                      return;
                    }
                    if (vm.recipientCurrency?.hasLowBalance == true) {
                      final r = await BsWrapper.bottomSheet(
                        context: context,
                        widget: const LowBalanceAlertSheet(),
                      );
                      if (r == true) {
                        if (context.mounted) {
                          BsWrapper.bottomSheet(
                              context: context, widget: TransferMethodModal());
                        }
                      }
                      return;
                    }
                    // Navigator.pushNamed(
                    //     context, RoutePath.sendMoneyMethodScreen);

                    BsWrapper.bottomSheet(
                        context: context, widget: TransferMethodModal());
                  },
                  online: vm.btnEnabled && !isCurrencyLimitsExceeded),
              YBox(100),
            ],
          ),
        ),
      );
    });
  }

  SendMoneyParams _getSendMoneyParams(String transferMethod) {
    final sendVm = context.read<SendMoneyVM>();
    final requireParams = SendMoneyParams(
      sourceCurrencyId: sendVm.fromConvertWallet?.currency?.id ?? 0,
      targetCurrencyId: sendVm.recipientCurrency?.id ?? 0,
      amount: sendVm.fromDecimalValue,
      rate: sendVm.conversionRate?.rate?.rate,
      transactionPurposeId:
          context.read<TransactionVM>().selectedReason?.id ?? 0,
    );

    switch (transferMethod) {
      case TransferMethod.bankTransfer:
      case TransferMethod.mobileMoney:
        return requireParams.copyWith(
          destinationBankUuid: widget.arg?.beneficiary?.institutionCode,
          destinationBankAccountNumber:
              widget.arg?.beneficiary?.accountIdentifier,
          accountName: widget.arg?.beneficiary?.accountName,
          transferMethod: widget.arg?.beneficiary?.transferMethod,
          bankName: widget.arg?.beneficiary?.institutionName,
        );
      case TransferMethod.interac:
        return requireParams.copyWith(
          transferMethod: widget.arg?.beneficiary?.transferMethod,
          interacFirstName: widget.arg?.beneficiary?.firstName,
          interacLastName: widget.arg?.beneficiary?.lastName,
          interacEmail: widget.arg?.beneficiary?.accountIdentifier,
        );

      case TransferMethod.korrency:
        return requireParams.copyWith(
          transferMethod: widget.arg?.beneficiary?.transferMethod,
          korrencyUsername: widget.arg?.beneficiary?.firstName,
        );
      default:
        return requireParams;
    }

    // destinationBankUuid: widget.arg?.beneficiary?.institutionCode,
    // destinationBankAccountNumber: widget.arg?.beneficiary?.accountIdentifier,
    // accountName: widget.arg?.beneficiary?.accountName,
    // transferMethod: widget.arg?.beneficiary?.transferMethod,
    // bankName: widget.arg?.beneficiary?.institutionName,
    // // korrencyUsername:
    // //     widget.arg?.beneficiary?.korrencyUsername,
  }
}

class SendTransButton extends StatelessWidget {
  const SendTransButton({
    super.key,
    required this.buttonText,
    this.bgColor,
    this.textColor,
    this.onTap,
  });

  final String buttonText;
  final Color? bgColor;
  final Color? textColor;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(8),
          vertical: Sizer.height(6),
        ),
        decoration: BoxDecoration(
          color: bgColor ?? AppColors.blueFD,
          borderRadius: BorderRadius.all(
            Radius.circular(8),
          ),
        ),
        child: Text(
          buttonText,
          style: AppTypography.text12.copyWith(
            color: textColor ?? AppColors.blueE5,
          ),
        ),
      ),
    );
  }
}
