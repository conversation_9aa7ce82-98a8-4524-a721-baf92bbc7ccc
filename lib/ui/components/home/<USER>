import 'package:korrency/core/core.dart';

class HomeTab extends StatelessWidget {
  const HomeTab({
    super.key,
    this.isActive = false,
    required this.text,
    this.onTap,
  });

  final bool isActive;
  final String text;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Skeleton.replace(
      replacement: Bone(
        height: Sizer.height(44),
        borderRadius: BorderRadius.circular(
          Sizer.radius(12),
        ),
      ),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(4),
          ),
          height: Sizer.height(44),
          decoration: BoxDecoration(
            color: isActive ? AppColors.primaryBlue : AppColors.blueFD,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              text,
              style: AppTypography.text13.semiBold
                  .withCustomHeight(1.2)
                  .copyWith(
                    color: isActive ? AppColors.white : AppColors.primaryBlue,
                  ),
            ),
          ),
        ),
      ),
    );
  }
}
